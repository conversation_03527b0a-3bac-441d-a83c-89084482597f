<?php
/**
 * Reviews Class
 *
 * @package Q-Updater
 */

if (!defined('ABSPATH')) {
    exit; // Exit if accessed directly
}

class Q_Updater_Reviews
{
    private $parent;
    private $table_name;
    private $db_version = '1.0';

    /**
     * Constructor
     *
     * @param Q_Updater $parent Parent class instance
     */
    public function __construct($parent)
    {
        global $wpdb;

        $this->parent = $parent;
        $this->table_name = $wpdb->prefix . 'qu_plugin_reviews';

        add_action('init', [$this, 'init']);
    }

    /**
     * Initialize reviews functionality after translations are loaded
     */
    public function init()
    {
        // Register AJAX handlers
        add_action('wp_ajax_qu_submit_review', [$this, 'ajax_submit_review']);
        add_action('wp_ajax_qu_delete_review', [$this, 'ajax_delete_review']);

        // Check if we need to create the reviews table
        if (get_option('q_updater_create_reviews_table', false)) {
            $this->create_table();
            delete_option('q_updater_create_reviews_table');
        }
    }

    /**
     * Create the reviews database table
     */
    public function create_table()
    {
        global $wpdb;

        $charset_collate = $wpdb->get_charset_collate();

        $sql = "CREATE TABLE $this->table_name (
            id mediumint(9) NOT NULL AUTO_INCREMENT,
            plugin_slug varchar(100) NOT NULL,
            user_id bigint(20) NOT NULL,
            user_name varchar(100) NOT NULL,
            user_email varchar(100) NOT NULL,
            rating tinyint(1) NOT NULL,
            review_text text NOT NULL,
            review_date datetime DEFAULT CURRENT_TIMESTAMP NOT NULL,
            status varchar(20) DEFAULT 'approved' NOT NULL,
            PRIMARY KEY  (id)
        ) $charset_collate;";

        require_once(ABSPATH . 'wp-admin/includes/upgrade.php');
        dbDelta($sql);

        add_option('qu_reviews_db_version', $this->db_version);
    }

    /**
     * AJAX handler for submitting a review
     */
    public function ajax_submit_review()
    {
        // Verify nonce using standard WordPress function
        check_ajax_referer('qu_submit_review', 'nonce');

        // Check user capability
        if (!current_user_can('edit_posts')) {
            wp_send_json_error([
                'message' => __('You do not have permission to perform this action.', 'q-updater'),
                'code' => 'insufficient_permissions'
            ], 403);
            exit;
        }

        // Additional security headers
        header('X-Content-Type-Options: nosniff');
        header('X-Frame-Options: SAMEORIGIN');

        // Get and sanitize data
        $plugin_slug = isset($_POST['plugin_slug']) ? sanitize_text_field($_POST['plugin_slug']) : '';
        $rating = isset($_POST['rating']) ? intval($_POST['rating']) : 0;
        $review_text = isset($_POST['review_text']) ? sanitize_textarea_field($_POST['review_text']) : '';

        // Validate data
        if (empty($plugin_slug)) {
            wp_send_json_error(['message' => __('Plugin slug is required.', 'q-updater')]);
        }

        if ($rating < 1 || $rating > 5) {
            wp_send_json_error(['message' => __('Rating must be between 1 and 5.', 'q-updater')]);
        }

        if (empty($review_text)) {
            wp_send_json_error(['message' => __('Review text is required.', 'q-updater')]);
        }

        // Get current user info
        $current_user = wp_get_current_user();

        // Insert review into database
        $result = $this->add_review([
            'plugin_slug' => $plugin_slug,
            'user_id' => $current_user->ID,
            'user_name' => $current_user->display_name,
            'user_email' => $current_user->user_email,
            'rating' => $rating,
            'review_text' => $review_text
        ]);

        if ($result) {
            // Send email notification
            $this->send_review_notification($plugin_slug, $rating, $review_text, $current_user);

            wp_send_json_success([
                'message' => __('Review submitted successfully.', 'q-updater'),
                'review' => $this->get_review($result)
            ]);
        } else {
            wp_send_json_error(['message' => __('Failed to submit review.', 'q-updater')]);
        }
    }

    /**
     * AJAX handler for deleting a review
     */
    public function ajax_delete_review()
    {
        // Verify nonce using standard WordPress function
        check_ajax_referer('qu_delete_review', 'nonce');

        // Check user capability
        if (!current_user_can('manage_options')) {
            wp_send_json_error([
                'message' => __('You do not have permission to perform this action.', 'q-updater'),
                'code' => 'insufficient_permissions'
            ], 403);
            exit;
        }

        // Additional security headers
        header('X-Content-Type-Options: nosniff');
        header('X-Frame-Options: SAMEORIGIN');

        // Get review ID
        $review_id = isset($_POST['review_id']) ? intval($_POST['review_id']) : 0;

        if ($review_id <= 0) {
            wp_send_json_error(['message' => __('Invalid review ID.', 'q-updater')]);
        }

        // Delete review
        $result = $this->delete_review($review_id);

        if ($result) {
            wp_send_json_success(['message' => __('Review deleted successfully.', 'q-updater')]);
        } else {
            wp_send_json_error(['message' => __('Failed to delete review.', 'q-updater')]);
        }
    }

    /**
     * Add a new review to the database
     *
     * @param array $review_data Review data
     * @return int|false The review ID on success, false on failure
     */
    public function add_review($review_data)
    {
        global $wpdb;

        $result = $wpdb->insert(
            $this->table_name,
            [
                'plugin_slug' => $review_data['plugin_slug'],
                'user_id' => $review_data['user_id'],
                'user_name' => $review_data['user_name'],
                'user_email' => $review_data['user_email'],
                'rating' => $review_data['rating'],
                'review_text' => $review_data['review_text'],
                'review_date' => current_time('mysql'),
                'status' => 'approved'
            ],
            [
                '%s', // plugin_slug
                '%d', // user_id
                '%s', // user_name
                '%s', // user_email
                '%d', // rating
                '%s', // review_text
                '%s', // review_date
                '%s'  // status
            ]
        );

        return $result ? $wpdb->insert_id : false;
    }

    /**
     * Get a single review by ID
     *
     * @param int $review_id Review ID
     * @return object|null Review object or null if not found
     */
    public function get_review($review_id)
    {
        global $wpdb;

        return $wpdb->get_row($wpdb->prepare(
            "SELECT * FROM $this->table_name WHERE id = %d",
            $review_id
        ));
    }

    /**
     * Get reviews for a specific plugin
     *
     * @param string $plugin_slug Plugin slug
     * @param int $limit Number of reviews to retrieve (0 for all)
     * @param int $offset Offset for pagination
     * @return array Array of review objects
     */
    public function get_plugin_reviews($plugin_slug, $limit = 0, $offset = 0)
    {
        global $wpdb;

        $sql = $wpdb->prepare(
            "SELECT * FROM $this->table_name WHERE plugin_slug = %s AND status = 'approved' ORDER BY review_date DESC",
            $plugin_slug
        );

        if ($limit > 0) {
            $sql .= $wpdb->prepare(" LIMIT %d, %d", $offset, $limit);
        }

        return $wpdb->get_results($sql);
    }

    /**
     * Get all reviews
     *
     * @param int $limit Number of reviews to retrieve (0 for all)
     * @param int $offset Offset for pagination
     * @return array Array of review objects
     */
    public function get_all_reviews($limit = 0, $offset = 0)
    {
        global $wpdb;

        $sql = "SELECT * FROM $this->table_name WHERE status = 'approved' ORDER BY review_date DESC";

        if ($limit > 0) {
            $sql .= $wpdb->prepare(" LIMIT %d, %d", $offset, $limit);
        }

        return $wpdb->get_results($sql);
    }

    /**
     * Delete a review
     *
     * @param int $review_id Review ID
     * @return bool True on success, false on failure
     */
    public function delete_review($review_id)
    {
        global $wpdb;

        return $wpdb->delete(
            $this->table_name,
            ['id' => $review_id],
            ['%d']
        );
    }

    /**
     * Get average rating for a plugin
     *
     * @param string $plugin_slug Plugin slug
     * @return float Average rating
     */
    public function get_average_rating($plugin_slug)
    {
        global $wpdb;

        $average = $wpdb->get_var($wpdb->prepare(
            "SELECT AVG(rating) FROM $this->table_name WHERE plugin_slug = %s AND status = 'approved'",
            $plugin_slug
        ));

        return round(floatval($average), 1);
    }

    /**
     * Get total number of reviews for a plugin
     *
     * @param string $plugin_slug Plugin slug
     * @return int Number of reviews
     */
    public function get_review_count($plugin_slug)
    {
        global $wpdb;

        return $wpdb->get_var($wpdb->prepare(
            "SELECT COUNT(*) FROM $this->table_name WHERE plugin_slug = %s AND status = 'approved'",
            $plugin_slug
        ));
    }

    /**
     * Send email notification about a new review
     *
     * @param string $plugin_slug Plugin slug
     * @param int $rating Rating
     * @param string $review_text Review text
     * @param WP_User $user User who submitted the review
     * @return bool True on success, false on failure
     */
    public function send_review_notification($plugin_slug, $rating, $review_text, $user)
    {
        // Get developer email directly from the option
        $developer_email = get_option('q_updater_developer_email', '');

        // Log the email retrieval for debugging
        error_log('Developer Email Option Name: q_updater_developer_email');
        error_log('Developer Email Value: ' . $developer_email);

        // If no developer email is set, use the admin email
        if (empty($developer_email)) {
            $developer_email = get_option('admin_email');
            error_log('Using Admin Email: ' . $developer_email);
        }

        // Get plugin data
        $plugin_manager = new Q_Updater_Plugin_Manager($this->parent);
        $plugin_data = $plugin_manager->get_safe_plugin_data("$plugin_slug/$plugin_slug.php");
        $plugin_name = $plugin_data ? $plugin_data['Name'] : $plugin_slug;

        // Prepare email subject and message
        $subject = sprintf(
            __('[%s] New review for %s', 'q-updater'),
            get_bloginfo('name'),
            $plugin_name
        );

        $message = sprintf(
            __("A new review has been submitted for %s:\n\n", 'q-updater'),
            $plugin_name
        );

        $message .= sprintf(
            __("Rating: %d/5\n\n", 'q-updater'),
            $rating
        );

        $message .= sprintf(
            __("Review:\n%s\n\n", 'q-updater'),
            $review_text
        );

        $message .= sprintf(
            __("Submitted by: %s (%s)\n", 'q-updater'),
            $user->display_name,
            $user->user_email
        );

        $message .= sprintf(
            __("\nView all reviews: %s", 'q-updater'),
            admin_url('options-general.php?page=q-updater&tab=reviews')
        );

        // Send email
        $headers = ['Content-Type: text/plain; charset=UTF-8'];

        // Log the email sending attempt
        error_log('Attempting to send review notification email to: ' . $developer_email);
        error_log('Email subject: ' . $subject);
        error_log('Email message: ' . $message);

        $result = wp_mail($developer_email, $subject, $message, $headers);

        // Log the result
        error_log('Email send result: ' . ($result ? 'Success' : 'Failed'));

        return $result;
    }

    /**
     * Render star rating HTML
     *
     * @param float $rating Rating value
     * @param bool $is_input Whether this is an input field
     * @return string HTML for star rating
     */
    public function render_star_rating($rating, $is_input = false)
    {
        $html = '<div class="qu-star-rating' . ($is_input ? ' qu-star-rating-input' : '') . '">';

        for ($i = 1; $i <= 5; $i++) {
            if ($is_input) {
                $html .= '<span class="qu-star' . ($i <= $rating ? ' qu-star-filled' : '') . '" data-rating="' . $i . '">';
                $html .= '<span class="dashicons ' . ($i <= $rating ? 'dashicons-star-filled' : 'dashicons-star-empty') . '"></span>';
                $html .= '</span>';
            } else {
                $html .= '<span class="qu-star' . ($i <= $rating ? ' qu-star-filled' : '') . '">';
                $html .= '<span class="dashicons ' . ($i <= $rating ? 'dashicons-star-filled' : 'dashicons-star-empty') . '"></span>';
                $html .= '</span>';
            }
        }

        $html .= '</div>';

        return $html;
    }
}
