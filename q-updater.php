<?php
/*
Plugin Name: Q-Updater
Plugin URI: https://github.com/shamielo/q-updater
Description: Automatically updates Q plugins from GitHub repositories.
Version: 1.3.3
Author: Shamielo
Author URI: https://github.com/shamielo
License: GPL2
Text Domain: q-updater
Domain Path: /languages
*/

if (!defined('ABSPATH')) {
    exit; // Exit if accessed directly
}

// Define plugin constants
define('Q_UPDATER_PLUGIN_FILE', __FILE__);
define('Q_UPDATER_PLUGIN_DIR', plugin_dir_path(__FILE__));
define('Q_UPDATER_PLUGIN_URL', plugin_dir_url(__FILE__));
define('Q_UPDATER_VERSION', '1.3.3');

/**
 * Activation hook
 */
function q_updater_activate()
{
    // Create necessary database tables and options
    add_option('q_updater_version', Q_UPDATER_VERSION);

    // Schedule the update check cron
    if (!wp_next_scheduled('q_updater_check_updates')) {
        wp_schedule_event(time(), 'daily', 'q_updater_check_updates');
    }

    // Create reviews table - we'll initialize it properly when the plugin loads
    add_option('q_updater_create_reviews_table', true);

    // Create analytics table - we'll initialize it properly when the plugin loads
    add_option('q_updater_create_analytics_table', true);
}

/**
 * Deactivation hook
 */
function q_updater_deactivate()
{
    // Clear scheduled hooks
    wp_clear_scheduled_hook('q_updater_check_updates');
    wp_clear_scheduled_hook('q_updater_background_update');
    wp_clear_scheduled_hook('qu_weekly_analytics_report');
}

register_activation_hook(__FILE__, 'q_updater_activate');
register_deactivation_hook(__FILE__, 'q_updater_deactivate');

// Clear any existing notices
delete_user_meta(get_current_user_id(), 'q_updater_dismissed_token_notice');
delete_user_meta(get_current_user_id(), 'q_updater_dismissed_update_notice');

// Include the main plugin class
require_once Q_UPDATER_PLUGIN_DIR . 'includes/class-q-updater.php';

// Initialize the plugin on plugins_loaded but load translations at init
function q_updater_init()
{
    global $q_updater;
    $q_updater = new Q_Updater();
}

// Load translations at init hook to avoid "loaded too early" warnings
function q_updater_load_textdomain()
{
    load_plugin_textdomain(
        'q-updater',
        false,
        dirname(plugin_basename(Q_UPDATER_PLUGIN_FILE)) . '/languages/'
    );
}

add_action('plugins_loaded', 'q_updater_init');
add_action('init', 'q_updater_load_textdomain');
