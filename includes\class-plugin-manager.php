<?php
/**
 * Plugin Manager Class
 *
 * @package Q-Updater
 */

if (!defined('ABSPATH')) {
    exit; // Exit if accessed directly
}

class Q_Updater_Plugin_Manager
{
    private $parent;
    private $security;

    /**
     * Constructor
     *
     * @param Q_Updater $parent Parent class instance
     */
    public function __construct($parent)
    {
        $this->parent = $parent;
        $this->security = $parent->get_security();
    }

    /**
     * Get installed GitHub plugins
     *
     * @param bool $only_q_plugins Whether to only return Q plugins (with q- prefix)
     * @param int $page Current page number (1-based)
     * @param int $per_page Number of plugins per page (0 for all)
     * @return array List of installed GitHub plugins with pagination info
     */
    public function get_installed_q_plugins($only_q_plugins = false, $page = 1, $per_page = 0)
    {
        $installed_plugins = [];

        if (!function_exists('get_plugins')) {
            require_once ABSPATH . 'wp-admin/includes/plugin.php';
        }

        $all_plugins = get_plugins();

        // Get custom repository mappings
        $custom_mappings = get_option($this->parent->get_option_name('custom_repo_mappings'), []);

        foreach ($all_plugins as $plugin_file => $plugin_data) {
            $slug = dirname($plugin_file);
            if (empty($slug)) {
                continue; // Skip plugins without a directory
            }

            $plugin_path = WP_PLUGIN_DIR . '/' . $plugin_file;
            if (!file_exists($plugin_path)) {
                continue; // Skip if file doesn't exist
            }

            // Check if this is a Q plugin
            $is_q_plugin = (strpos($slug, 'q-') === 0);

            // If only_q_plugins is true, skip non-Q plugins
            if ($only_q_plugins && !$is_q_plugin) {
                continue;
            }

            // Check if there's a custom mapping for this plugin
            if (isset($custom_mappings[$slug])) {
                $installed_plugins[$slug] = $custom_mappings[$slug];
            } else if ($is_q_plugin) {
                // Use default format for Q plugins
                $installed_plugins[$slug] = "shamielo/$slug";
            } else {
                // For non-Q plugins, check if they might be GitHub plugins
                $github_info = $this->detect_github_plugin($plugin_file, $plugin_data);
                if ($github_info) {
                    $installed_plugins[$slug] = $github_info;
                }
            }
        }

        // Apply filter to allow other plugins to modify the list
        $installed_plugins = apply_filters('q_updater_managed_plugins', $installed_plugins);

        // If pagination is not requested, return all plugins
        if ($per_page <= 0) {
            return $installed_plugins;
        }

        // Calculate pagination
        $total_plugins = count($installed_plugins);
        $total_pages = ceil($total_plugins / $per_page);
        $page = max(1, min($page, $total_pages)); // Ensure page is within valid range
        $offset = ($page - 1) * $per_page;

        // Get plugins for current page
        $paginated_plugins = array_slice($installed_plugins, $offset, $per_page, true);

        return [
            'plugins' => $paginated_plugins,
            'pagination' => [
                'current_page' => $page,
                'per_page' => $per_page,
                'total_plugins' => $total_plugins,
                'total_pages' => $total_pages,
                'has_prev' => $page > 1,
                'has_next' => $page < $total_pages,
                'prev_page' => $page > 1 ? $page - 1 : null,
                'next_page' => $page < $total_pages ? $page + 1 : null
            ]
        ];
    }

    /**
     * Detect if a plugin is likely from GitHub based on its metadata
     *
     * @param string $plugin_file Plugin file path
     * @param array $plugin_data Plugin data from get_plugins()
     * @return string|false GitHub repository path or false if not detected
     */
    private function detect_github_plugin($plugin_file, $plugin_data)
    {
        // Check if plugin URI contains github.com
        if (!empty($plugin_data['PluginURI']) && strpos($plugin_data['PluginURI'], 'github.com') !== false) {
            // Extract username/repo from the URL
            preg_match('/github\.com\/([a-zA-Z0-9\-_.]+)\/([a-zA-Z0-9\-_.]+)/', $plugin_data['PluginURI'], $matches);
            if (count($matches) === 3) {
                return $matches[1] . '/' . $matches[2];
            }
        }

        // Check if author URI contains github.com
        if (!empty($plugin_data['AuthorURI']) && strpos($plugin_data['AuthorURI'], 'github.com') !== false) {
            // Extract username from the URL
            preg_match('/github\.com\/([a-zA-Z0-9\-_.]+)/', $plugin_data['AuthorURI'], $matches);
            if (count($matches) === 2) {
                $username = $matches[1];
                $slug = dirname($plugin_file);
                return $username . '/' . $slug;
            }
        }

        return false;
    }

    /**
     * Get all installed plugins
     *
     * @return array List of all installed plugins with their data
     */
    public function get_all_installed_plugins()
    {
        $installed_plugins = [];

        if (!function_exists('get_plugins')) {
            require_once ABSPATH . 'wp-admin/includes/plugin.php';
        }

        $all_plugins = get_plugins();
        $github_plugins = $this->get_installed_q_plugins(false);
        $active_plugins = get_option('active_plugins', []);

        foreach ($all_plugins as $plugin_file => $plugin_data) {
            $slug = dirname($plugin_file);
            if (empty($slug)) {
                $slug = $plugin_file; // For plugins without a directory
            }

            $is_active = in_array($plugin_file, $active_plugins);
            $is_github_managed = isset($github_plugins[$slug]);
            $github_repo = $is_github_managed ? $github_plugins[$slug] : '';

            // Determine plugin type with more specific classification
            $plugin_type = 'wordpress.org';
            if (strpos($slug, 'q-') === 0) {
                $plugin_type = 'q-plugin';
            } elseif ($is_github_managed) {
                // Check if it's from GitLab or Bitbucket
                if (strpos($github_repo, 'gitlab:') === 0) {
                    $plugin_type = 'gitlab';
                } elseif (strpos($github_repo, 'bitbucket:') === 0) {
                    $plugin_type = 'bitbucket';
                } else {
                    $plugin_type = 'github';
                }
            }

            // Check if plugin has GitHub metadata even if not managed
            $has_github_metadata = false;
            if (!$is_github_managed) {
                $has_github_metadata = (
                    !empty($plugin_data['PluginURI']) && strpos($plugin_data['PluginURI'], 'github.com') !== false
                ) || (
                    !empty($plugin_data['AuthorURI']) && strpos($plugin_data['AuthorURI'], 'github.com') !== false
                );
            }

            $installed_plugins[$slug] = [
                'name' => $plugin_data['Name'],
                'version' => $plugin_data['Version'],
                'description' => $plugin_data['Description'],
                'author' => $plugin_data['Author'],
                'plugin_uri' => $plugin_data['PluginURI'] ?? '',
                'author_uri' => $plugin_data['AuthorURI'] ?? '',
                'plugin_file' => $plugin_file,
                'is_active' => $is_active,
                'is_github_managed' => $is_github_managed,
                'has_github_metadata' => $has_github_metadata,
                'github_repo' => $github_repo,
                'type' => $plugin_type
            ];
        }

        return $installed_plugins;
    }

    /**
     * Get plugin version info
     *
     * @param string $slug Plugin slug
     * @param string $repo GitHub repository
     * @return array|false Plugin version information or false on failure
     */
    public function get_plugin_version_info($slug, $repo)
    {
        // First try the standard Q plugin structure
        $plugin_data = $this->get_safe_plugin_data("$slug/$slug.php");

        // If not found, try to find the main plugin file
        if (!$plugin_data) {
            if (!function_exists('get_plugins')) {
                require_once ABSPATH . 'wp-admin/includes/plugin.php';
            }

            $all_plugins = get_plugins();
            foreach ($all_plugins as $plugin_file => $data) {
                $plugin_slug = dirname($plugin_file);
                if ($plugin_slug === $slug) {
                    $plugin_data = $data;
                    break;
                }
            }

            // If still not found, return false
            if (!$plugin_data) {
                return false;
            }
        }

        $current_version = $plugin_data['Version'];
        $github_api = new Q_Updater_GitHub_API($this->parent);
        $latest = $github_api->get_latest_release($repo);

        // Handle WP_Error objects
        if (is_wp_error($latest)) {
            return [
                'name' => $plugin_data['Name'],
                'current_version' => $current_version,
                'latest_version' => false,
                'has_update' => false,
                'error' => $latest->get_error_message()
            ];
        }

        return [
            'name' => $plugin_data['Name'],
            'current_version' => $current_version,
            'latest_version' => $latest ? $latest['version'] : false,
            'has_update' => $latest ? version_compare($current_version, $latest['version'], '<') : false,
            'description' => isset($plugin_data['Description']) ? $plugin_data['Description'] : '',
            'author' => isset($plugin_data['Author']) ? $plugin_data['Author'] : '',
            'plugin_uri' => isset($plugin_data['PluginURI']) ? $plugin_data['PluginURI'] : '',
            'github_repo' => $repo
        ];
    }

    /**
     * Get safe plugin data
     *
     * @param string $plugin_file Plugin file path
     * @return array|false Plugin data or false on failure
     */
    public function get_safe_plugin_data($plugin_file)
    {
        $full_path = WP_PLUGIN_DIR . '/' . $plugin_file;

        // If the exact file doesn't exist, try to find the main plugin file
        if (!file_exists($full_path)) {
            $plugin_dir = dirname($full_path);
            $plugin_slug = basename($plugin_dir);

            // If the plugin directory exists
            if (is_dir($plugin_dir)) {
                // Try to find a PHP file with the same name as the directory
                $potential_main_file = $plugin_dir . '/' . $plugin_slug . '.php';
                if (file_exists($potential_main_file)) {
                    $full_path = $potential_main_file;
                } else {
                    // Look for any PHP file in the root of the plugin directory
                    $php_files = glob($plugin_dir . '/*.php');
                    if (!empty($php_files)) {
                        // Check each PHP file for plugin headers
                        foreach ($php_files as $php_file) {
                            if (!function_exists('get_plugin_data')) {
                                require_once ABSPATH . 'wp-admin/includes/plugin.php';
                            }

                            $plugin_data = get_plugin_data($php_file);
                            if (!empty($plugin_data['Name'])) {
                                // Found a valid plugin file
                                $full_path = $php_file;
                                break;
                            }
                        }
                    }
                }
            }

            // If we still don't have a valid file, return false
            if (!file_exists($full_path)) {
                return false;
            }
        }

        if (!function_exists('get_plugin_data')) {
            require_once ABSPATH . 'wp-admin/includes/plugin.php';
        }

        return get_plugin_data($full_path);
    }

    /**
     * Check plugin dependencies
     *
     * @param string $plugin_slug Plugin slug
     * @return array Result with 'status' (bool) and 'missing' (array) keys
     */
    public function check_dependencies($plugin_slug)
    {
        // Find the main plugin file
        $plugin_file = $this->find_plugin_file($plugin_slug);
        if (!$plugin_file) {
            return ['status' => true, 'missing' => []]; // Can't find plugin, assume no dependencies
        }

        $plugin_data = $this->get_safe_plugin_data($plugin_file);

        if (!$plugin_data || empty($plugin_data['RequiresPlugin'])) {
            return ['status' => true, 'missing' => []]; // No dependencies specified
        }

        $required_plugins = explode(',', $plugin_data['RequiresPlugin']);
        $missing_dependencies = [];

        foreach ($required_plugins as $required_plugin) {
            $required_plugin = trim($required_plugin);
            if (empty($required_plugin))
                continue;

            // Check for version constraints (e.g., "q-core >=1.2.0")
            $version_constraint = null;
            $plugin_name = $required_plugin;

            if (preg_match('/^([a-zA-Z0-9\-_\/\.]+)\s+([<>=]{1,2}[0-9\.]+)$/', $required_plugin, $matches)) {
                $plugin_name = trim($matches[1]);
                $version_constraint = trim($matches[2]);
            }

            // Check if plugin exists and is active
            if (!function_exists('is_plugin_active')) {
                include_once(ABSPATH . 'wp-admin/includes/plugin.php');
            }

            // First check if it's a direct plugin file reference
            $is_active = is_plugin_active($plugin_name);

            // If not active, try to find it by slug
            if (!$is_active) {
                // Try to find the plugin file by slug
                $dependency_file = $this->find_plugin_file($plugin_name);
                if ($dependency_file) {
                    $is_active = is_plugin_active($dependency_file);
                }
            }

            if (!$is_active) {
                $missing_dependencies[] = [
                    'name' => $plugin_name,
                    'constraint' => $version_constraint,
                    'reason' => 'not_active'
                ];
                continue;
            }

            // If there's a version constraint, check it
            if ($version_constraint) {
                // Get the actual plugin version
                $dependency_data = null;

                if (strpos($plugin_name, '/') !== false) {
                    // It's a direct plugin file reference
                    $dependency_data = $this->get_safe_plugin_data($plugin_name);
                } else {
                    // Try to find by slug
                    $dependency_file = $this->find_plugin_file($plugin_name);
                    if ($dependency_file) {
                        $dependency_data = $this->get_safe_plugin_data($dependency_file);
                    }
                }

                if ($dependency_data && !empty($dependency_data['Version'])) {
                    $actual_version = $dependency_data['Version'];

                    // Parse the constraint
                    preg_match('/([<>=]{1,2})([0-9\.]+)/', $version_constraint, $constraint_parts);
                    if (count($constraint_parts) === 3) {
                        $operator = $constraint_parts[1];
                        $required_version = $constraint_parts[2];

                        // Check if the version meets the constraint
                        if (!version_compare($actual_version, $required_version, $operator)) {
                            $missing_dependencies[] = [
                                'name' => $plugin_name,
                                'constraint' => $version_constraint,
                                'actual_version' => $actual_version,
                                'required_version' => $required_version,
                                'reason' => 'version_mismatch'
                            ];
                        }
                    }
                }
            }
        }

        return [
            'status' => empty($missing_dependencies),
            'missing' => $missing_dependencies
        ];
    }

    /**
     * Normalize repository format
     *
     * @param string $repo Repository in various formats (username/repo, full URL, etc.)
     * @return string|false Normalized repository in username/repo format or false if invalid
     */
    public function normalize_repository_format($repo)
    {
        // If empty, return false
        if (empty($repo)) {
            return false;
        }

        // Remove any whitespace
        $repo = trim($repo);

        // Check for repository type prefixes
        $repo_type = '';
        if (strpos($repo, 'gitlab:') === 0) {
            $repo_type = 'gitlab:';
            $repo = substr($repo, 7); // Remove 'gitlab:' prefix
        } elseif (strpos($repo, 'bitbucket:') === 0) {
            $repo_type = 'bitbucket:';
            $repo = substr($repo, 10); // Remove 'bitbucket:' prefix
        }

        // If already in username/repo format, validate and return
        if (preg_match('/^[a-zA-Z0-9\-_.]+\/[a-zA-Z0-9\-_.]+$/', $repo)) {
            return $repo_type . $repo;
        }

        // Handle GitHub URLs
        if (strpos($repo, 'github.com') !== false) {
            // Extract username/repo from URL
            preg_match('/github\.com\/([a-zA-Z0-9\-_.]+)\/([a-zA-Z0-9\-_.]+)/', $repo, $matches);
            if (count($matches) === 3) {
                return $matches[1] . '/' . $matches[2];
            }
        }

        // Handle GitLab URLs
        if (strpos($repo, 'gitlab.com') !== false) {
            // Extract username/repo from URL
            preg_match('/gitlab\.com\/([a-zA-Z0-9\-_.]+)\/([a-zA-Z0-9\-_.]+)/', $repo, $matches);
            if (count($matches) === 3) {
                // For GitLab, we'll prefix with 'gitlab:'
                return 'gitlab:' . $matches[1] . '/' . $matches[2];
            }
        }

        // Handle Bitbucket URLs
        if (strpos($repo, 'bitbucket.org') !== false) {
            // Extract username/repo from URL
            preg_match('/bitbucket\.org\/([a-zA-Z0-9\-_.]+)\/([a-zA-Z0-9\-_.]+)/', $repo, $matches);
            if (count($matches) === 3) {
                // For Bitbucket, we'll prefix with 'bitbucket:'
                return 'bitbucket:' . $matches[1] . '/' . $matches[2];
            }
        }

        // Handle URLs with .git extension
        if (preg_match('/([a-zA-Z0-9\-_.]+)\/([a-zA-Z0-9\-_.]+)\.git$/', $repo, $matches)) {
            if (count($matches) === 3) {
                return $repo_type . $matches[1] . '/' . $matches[2];
            }
        }

        // Handle SSH URLs (**************:username/repo.git)
        if (preg_match('/git@([a-zA-Z0-9\-_.]+):([a-zA-Z0-9\-_.]+)\/([a-zA-Z0-9\-_.]+)/', $repo, $matches)) {
            if (count($matches) === 4) {
                $host = $matches[1];
                $repo_type = '';

                if ($host === 'github.com') {
                    $repo_type = '';
                } elseif ($host === 'gitlab.com') {
                    $repo_type = 'gitlab:';
                } elseif ($host === 'bitbucket.org') {
                    $repo_type = 'bitbucket:';
                }

                return $repo_type . $matches[2] . '/' . str_replace('.git', '', $matches[3]);
            }
        }

        // If we couldn't normalize the format, return false
        return false;
    }

    /**
     * Find the main plugin file for a given slug
     *
     * @param string $plugin_slug Plugin slug
     * @return string|false Plugin file path or false if not found
     */
    public function find_plugin_file($plugin_slug)
    {
        // If it already contains a slash, it's a direct plugin file reference
        if (strpos($plugin_slug, '/') !== false) {
            return $plugin_slug;
        }

        // Try the standard format first
        $standard_file = "$plugin_slug/$plugin_slug.php";
        $full_path = WP_PLUGIN_DIR . '/' . $standard_file;

        if (file_exists($full_path)) {
            return $standard_file;
        }

        // If not found, search all plugins
        if (!function_exists('get_plugins')) {
            require_once ABSPATH . 'wp-admin/includes/plugin.php';
        }

        $all_plugins = get_plugins();
        foreach ($all_plugins as $plugin_file => $plugin_data) {
            $slug = dirname($plugin_file);
            if ($slug === $plugin_slug) {
                return $plugin_file;
            }
        }

        return false;
    }

    /**
     * Get plugin dependencies
     *
     * @param string $plugin_slug Plugin slug
     * @return array Array of dependencies with their details
     */
    public function get_plugin_dependencies($plugin_slug)
    {
        $plugin_file = $this->find_plugin_file($plugin_slug);
        if (!$plugin_file) {
            return [];
        }

        $plugin_data = $this->get_safe_plugin_data($plugin_file);
        if (!$plugin_data || empty($plugin_data['RequiresPlugin'])) {
            return [];
        }

        $required_plugins = explode(',', $plugin_data['RequiresPlugin']);
        $dependencies = [];

        foreach ($required_plugins as $required_plugin) {
            $required_plugin = trim($required_plugin);
            if (empty($required_plugin))
                continue;

            // Check for version constraints (e.g., "q-core >=1.2.0")
            $version_constraint = null;
            $plugin_name = $required_plugin;

            if (preg_match('/^([a-zA-Z0-9\-_\/\.]+)\s+([<>=]{1,2}[0-9\.]+)$/', $required_plugin, $matches)) {
                $plugin_name = trim($matches[1]);
                $version_constraint = trim($matches[2]);
            }

            $dependencies[] = [
                'name' => $plugin_name,
                'constraint' => $version_constraint
            ];
        }

        return $dependencies;
    }

    /**
     * Install plugin dependencies
     *
     * @param string $plugin_slug Plugin slug
     * @return array Result with 'success' (bool) and 'message' (string) keys
     */
    public function install_dependencies($plugin_slug)
    {
        $dependencies = $this->get_plugin_dependencies($plugin_slug);
        if (empty($dependencies)) {
            return [
                'success' => true,
                'message' => __('No dependencies found', 'q-updater')
            ];
        }

        $results = [];
        $all_success = true;

        foreach ($dependencies as $dependency) {
            $dep_name = $dependency['name'];

            // Check if already installed and active
            if (!function_exists('is_plugin_active')) {
                include_once(ABSPATH . 'wp-admin/includes/plugin.php');
            }

            $is_active = false;

            // First check if it's a direct plugin file reference
            if (strpos($dep_name, '/') !== false) {
                $is_active = is_plugin_active($dep_name);
            } else {
                // Try to find the plugin file by slug
                $dependency_file = $this->find_plugin_file($dep_name);
                if ($dependency_file) {
                    $is_active = is_plugin_active($dependency_file);
                }
            }

            if ($is_active) {
                // Check version constraint if specified
                if (!empty($dependency['constraint'])) {
                    $dependency_data = null;

                    if (strpos($dep_name, '/') !== false) {
                        // It's a direct plugin file reference
                        $dependency_data = $this->get_safe_plugin_data($dep_name);
                    } else {
                        // Try to find by slug
                        $dependency_file = $this->find_plugin_file($dep_name);
                        if ($dependency_file) {
                            $dependency_data = $this->get_safe_plugin_data($dependency_file);
                        }
                    }

                    if ($dependency_data && !empty($dependency_data['Version'])) {
                        $actual_version = $dependency_data['Version'];

                        // Parse the constraint
                        preg_match('/([<>=]{1,2})([0-9\.]+)/', $dependency['constraint'], $constraint_parts);
                        if (count($constraint_parts) === 3) {
                            $operator = $constraint_parts[1];
                            $required_version = $constraint_parts[2];

                            // Check if the version meets the constraint
                            if (!version_compare($actual_version, $required_version, $operator)) {
                                $results[] = [
                                    'name' => $dep_name,
                                    'success' => false,
                                    'message' => sprintf(
                                        __('Version constraint not met: %s %s (current: %s)', 'q-updater'),
                                        $dep_name,
                                        $dependency['constraint'],
                                        $actual_version
                                    )
                                ];
                                $all_success = false;
                                continue;
                            }
                        }
                    }
                }

                $results[] = [
                    'name' => $dep_name,
                    'success' => true,
                    'message' => __('Already installed and active', 'q-updater')
                ];
                continue;
            }

            // Try to install the dependency
            // First, check if it's a Q plugin or has a custom mapping
            $custom_mappings = get_option($this->parent->get_option_name('custom_repo_mappings'), []);
            $repo = null;

            if (isset($custom_mappings[$dep_name])) {
                $repo = $custom_mappings[$dep_name];
            } else if (strpos($dep_name, 'q-') === 0) {
                $repo = "shamielo/$dep_name";
            }

            if ($repo) {
                // We have a repository, try to install it
                require_once(ABSPATH . 'wp-admin/includes/class-wp-upgrader.php');
                require_once(ABSPATH . 'wp-admin/includes/plugin.php');

                $github_api = new Q_Updater_GitHub_API($this->parent);
                $download_url = $github_api->get_github_download_url($repo);

                if (is_wp_error($download_url)) {
                    $results[] = [
                        'name' => $dep_name,
                        'success' => false,
                        'message' => sprintf(
                            __('Failed to get download URL: %s', 'q-updater'),
                            $download_url->get_error_message()
                        )
                    ];
                    $all_success = false;
                    continue;
                }

                $upgrader = new Plugin_Upgrader(new WP_Ajax_Upgrader_Skin());
                $install_result = $upgrader->install($download_url);

                if ($install_result === true) {
                    // Activate the plugin
                    $plugin_file = $this->find_plugin_file($dep_name);
                    if ($plugin_file) {
                        activate_plugin($plugin_file);
                        $results[] = [
                            'name' => $dep_name,
                            'success' => true,
                            'message' => __('Installed and activated successfully', 'q-updater')
                        ];
                    } else {
                        $results[] = [
                            'name' => $dep_name,
                            'success' => false,
                            'message' => __('Installed but could not be activated', 'q-updater')
                        ];
                        $all_success = false;
                    }
                } else {
                    $results[] = [
                        'name' => $dep_name,
                        'success' => false,
                        'message' => __('Installation failed', 'q-updater')
                    ];
                    $all_success = false;
                }
            } else {
                $results[] = [
                    'name' => $dep_name,
                    'success' => false,
                    'message' => __('No repository mapping found', 'q-updater')
                ];
                $all_success = false;
            }
        }

        return [
            'success' => $all_success,
            'results' => $results
        ];
    }

    /**
     * AJAX handler for installing plugin from repository
     *
     * Handles installation of plugins from GitHub, GitLab, and Bitbucket repositories.
     * The method implements security checks, rate limiting, and input validation
     * to ensure secure and reliable operation.
     */
    public function install_github_plugin()
    {
        // Verify nonce
        check_ajax_referer('bulk_update_q_plugins', 'nonce');

        // Check user capabilities
        if (!current_user_can('install_plugins')) {
            wp_send_json_error(__('Insufficient permissions. You need the install_plugins capability.', 'q-updater'));
        }

        // Rate limiting - prevent abuse
        $user_id = get_current_user_id();
        $rate_key = 'q_updater_install_rate_' . $user_id;
        $rate_data = get_transient($rate_key);

        if ($rate_data) {
            $count = $rate_data['count'];
            $time = $rate_data['time'];

            // Limit to 5 installations per minute
            if ($count >= 5 && (time() - $time) < 60) {
                wp_send_json_error(__('Rate limit exceeded. Please try again in a minute.', 'q-updater'));
            }

            // Update count
            set_transient($rate_key, [
                'count' => $count + 1,
                'time' => $time
            ], 60);
        } else {
            // First request
            set_transient($rate_key, [
                'count' => 1,
                'time' => time()
            ], 60);
        }

        // Validate and sanitize input
        $repo = isset($_POST['repo']) ? sanitize_text_field($_POST['repo']) : '';
        $version = isset($_POST['version']) ? sanitize_text_field($_POST['version']) : '';

        if (empty($repo)) {
            wp_send_json_error(__('Repository is required', 'q-updater'));
        }

        // Determine repository type (github, gitlab, bitbucket)
        $repo_type = 'github'; // Default to GitHub

        // Check if repo has a prefix indicating the source
        if (strpos($repo, 'gitlab:') === 0) {
            $repo_type = 'gitlab';
            $repo = substr($repo, 7); // Remove 'gitlab:' prefix
        } elseif (strpos($repo, 'bitbucket:') === 0) {
            $repo_type = 'bitbucket';
            $repo = substr($repo, 10); // Remove 'bitbucket:' prefix
        }

        // Log the repository type and path
        error_log("Q-Updater: Installing plugin from {$repo_type} repository: {$repo}");

        // Normalize and validate repository format
        $normalized_repo = $this->normalize_repository_format($repo);
        if (!$normalized_repo) {
            // If normalization failed, try the security validation as a fallback
            if (!$this->security->validate_repository_name($repo)) {
                wp_send_json_error(__('Invalid repository format. Use: username/repository or a valid repository URL', 'q-updater'));
            } else {
                $normalized_repo = $repo;
            }
        } else {
            // Use the normalized repository
            $repo = $normalized_repo;
        }

        // Validate version if provided
        if (!empty($version) && !preg_match('/^[a-zA-Z0-9\.\-_]+$/', $version)) {
            wp_send_json_error(__('Invalid version format. Use only alphanumeric characters, dots, hyphens, and underscores.', 'q-updater'));
        }

        // Get download URL
        $github_api = new Q_Updater_GitHub_API($this->parent);
        $download_url = $github_api->get_github_download_url($repo, $version);
        if (is_wp_error($download_url)) {
            wp_send_json_error($download_url->get_error_message());
        }

        // Ensure the URL is using HTTPS
        $download_url = $this->security->enforce_https($download_url);

        // Verify URL is from a trusted domain based on repository type
        $allowed_domains = [
            'github.com',
            'raw.githubusercontent.com',
            'api.github.com',
            'gitlab.com',
            'api.gitlab.com',
            'bitbucket.org',
            'api.bitbucket.org'
        ];
        $url_host = parse_url($download_url, PHP_URL_HOST);

        if (!in_array($url_host, $allowed_domains)) {
            wp_send_json_error(__('Download URL must be from a trusted domain.', 'q-updater'));
        }

        // Install the plugin
        require_once(ABSPATH . 'wp-admin/includes/class-wp-upgrader.php');
        require_once(ABSPATH . 'wp-admin/includes/plugin.php');

        $upgrader = new Plugin_Upgrader(new WP_Ajax_Upgrader_Skin());

        // Add extra security options for the download
        add_filter('http_request_args', function ($args, $url) {
            // Force SSL verification
            $args['sslverify'] = true;
            return $args;
        }, 10, 2);

        $result = $upgrader->install($download_url);

        if ($result === true) {
            // Get the installed plugin slug
            $plugin_slug = '';
            $repo_parts = explode('/', $repo);
            if (count($repo_parts) === 2) {
                // Try to determine the plugin slug from the repository name
                $repo_name = $repo_parts[1];

                // First, check if the plugin directory exists with the repo name as slug
                if (file_exists(WP_PLUGIN_DIR . '/' . $repo_name)) {
                    $plugin_slug = $repo_name;
                } else {
                    // If not found, try to find it in the installed plugins
                    $all_plugins = get_plugins();
                    foreach ($all_plugins as $plugin_file => $plugin_data) {
                        $slug = dirname($plugin_file);
                        if (empty($slug))
                            continue;

                        // Check if this plugin matches the repository
                        if ($slug === $repo_name) {
                            $plugin_slug = $slug;
                            break;
                        }
                    }

                    // If still not found, check custom mappings
                    if (empty($plugin_slug)) {
                        $installed_plugins = $this->get_installed_q_plugins(false);
                        foreach ($installed_plugins as $slug => $repo_url) {
                            if (!empty($repo_url) && !empty($repo) && strpos($repo_url, $repo) !== false) {
                                $plugin_slug = $slug;
                                break;
                            }
                        }
                    }
                }
            }

            // If we found a plugin slug, track the installation and check dependencies
            if (!empty($plugin_slug)) {
                $plugin_file = $this->find_plugin_file($plugin_slug);
                $plugin_data = $plugin_file ? $this->get_safe_plugin_data($plugin_file) : null;
                $version = $plugin_data ? $plugin_data['Version'] : '';

                // Track the installation in analytics
                $analytics = $this->parent->get_analytics();
                $analytics->track_installation($plugin_slug, $version, $repo_type);

                // Check dependencies
                $dependencies = $this->check_dependencies($plugin_slug);

                if (!$dependencies['status']) {
                    // Format missing dependencies for display
                    $missing_list = [];
                    foreach ($dependencies['missing'] as $missing) {
                        if ($missing['reason'] === 'not_active') {
                            $missing_list[] = sprintf(
                                __('%s is required but not active', 'q-updater'),
                                $missing['name']
                            );
                        } else if ($missing['reason'] === 'version_mismatch') {
                            $missing_list[] = sprintf(
                                __('%s %s is required (current version: %s)', 'q-updater'),
                                $missing['name'],
                                $missing['constraint'],
                                $missing['actual_version']
                            );
                        }
                    }

                    // Add custom repository mapping if it doesn't exist
                    $custom_mappings = get_option($this->parent->get_option_name('custom_repo_mappings'), []);
                    if (!isset($custom_mappings[$plugin_slug])) {
                        $custom_mappings[$plugin_slug] = $repo;
                        update_option($this->parent->get_option_name('custom_repo_mappings'), $custom_mappings);
                    }

                    wp_send_json_success([
                        'message' => __('Plugin installed successfully but has missing dependencies', 'q-updater'),
                        'dependencies' => $missing_list,
                        'plugin_slug' => $plugin_slug
                    ]);
                } else {
                    // Add custom repository mapping if it doesn't exist
                    $custom_mappings = get_option($this->parent->get_option_name('custom_repo_mappings'), []);
                    if (!isset($custom_mappings[$plugin_slug])) {
                        $custom_mappings[$plugin_slug] = $repo;
                        update_option($this->parent->get_option_name('custom_repo_mappings'), $custom_mappings);
                    }

                    wp_send_json_success([
                        'message' => __('Plugin installed successfully', 'q-updater'),
                        'plugin_slug' => $plugin_slug
                    ]);
                }
            } else {
                // Add a generic mapping based on the repository name
                $repo_parts = explode('/', $repo);
                if (count($repo_parts) === 2) {
                    $repo_name = $repo_parts[1];
                    $custom_mappings = get_option($this->parent->get_option_name('custom_repo_mappings'), []);
                    $custom_mappings[$repo_name] = $repo;
                    update_option($this->parent->get_option_name('custom_repo_mappings'), $custom_mappings);
                }

                wp_send_json_success([
                    'message' => __('Plugin installed successfully', 'q-updater'),
                    'plugin_slug' => $repo_parts[1] ?? ''
                ]);
            }
        } else {
            wp_send_json_error('Installation failed. Please check repository details and permissions.');
        }
    }

    /**
     * AJAX handler for installing plugin dependencies
     */
    public function ajax_install_dependencies()
    {
        // Verify nonce
        if (!check_ajax_referer('bulk_update_q_plugins', 'nonce', false)) {
            wp_send_json_error([
                'message' => __('Security check failed. Please refresh the page and try again.', 'q-updater'),
                'code' => 'invalid_nonce'
            ], 403);
            exit;
        }

        // Check user capabilities
        if (!current_user_can('install_plugins')) {
            wp_send_json_error([
                'message' => __('Insufficient permissions. You need the install_plugins capability.', 'q-updater'),
                'code' => 'insufficient_permissions'
            ], 403);
            exit;
        }

        // Rate limiting - prevent abuse
        $user_id = get_current_user_id();
        $rate_key = 'q_updater_dep_install_rate_' . $user_id;
        $rate_data = get_transient($rate_key);

        if ($rate_data) {
            $count = $rate_data['count'];
            $time = $rate_data['time'];

            // Limit to 5 installations per minute
            if ($count >= 5 && (time() - $time) < 60) {
                wp_send_json_error([
                    'message' => __('Rate limit exceeded. Please try again in a minute.', 'q-updater'),
                    'code' => 'rate_limit_exceeded'
                ], 429);
                exit;
            }

            // Update count
            set_transient($rate_key, [
                'count' => $count + 1,
                'time' => $time
            ], 60);
        } else {
            // First request
            set_transient($rate_key, [
                'count' => 1,
                'time' => time()
            ], 60);
        }

        // Validate and sanitize input
        $plugin_slug = isset($_POST['plugin_slug']) ? sanitize_text_field($_POST['plugin_slug']) : '';

        if (empty($plugin_slug)) {
            wp_send_json_error([
                'message' => __('Plugin slug is required', 'q-updater'),
                'code' => 'missing_plugin_slug'
            ], 400);
            exit;
        }

        // Install dependencies
        $result = $this->install_dependencies($plugin_slug);

        if ($result['success']) {
            wp_send_json_success($result);
        } else {
            wp_send_json_error([
                'message' => __('Failed to install one or more dependencies', 'q-updater'),
                'code' => 'dependency_installation_failed',
                'data' => $result
            ], 500);
        }
    }

    /**
     * AJAX handler for loading paginated plugins
     */
    public function ajax_load_paginated_plugins()
    {
        // Verify nonce
        if (!wp_verify_nonce($_POST['nonce'], 'q_updater_bulk_update')) {
            wp_send_json_error(__('Security check failed', 'q-updater'));
        }

        // Check user permissions
        if (!current_user_can('manage_options')) {
            wp_send_json_error(__('Insufficient permissions', 'q-updater'));
        }

        // Get pagination parameters
        $page = isset($_POST['page']) ? max(1, intval($_POST['page'])) : 1;
        $per_page = isset($_POST['per_page']) ? max(1, min(50, intval($_POST['per_page']))) : 6;

        // Get plugins with pagination
        $result = $this->get_installed_q_plugins(true, $page, $per_page);

        if (!is_array($result) || !isset($result['plugins'])) {
            wp_send_json_error(__('Failed to load plugins', 'q-updater'));
        }

        $plugins_html = $this->render_plugins_grid($result['plugins']);

        wp_send_json_success([
            'html' => $plugins_html,
            'pagination' => $result['pagination']
        ]);
    }

    /**
     * Render plugins grid HTML
     *
     * @param array $plugins Array of plugins to render
     * @return string HTML content
     */
    private function render_plugins_grid($plugins)
    {
        if (empty($plugins)) {
            return '<div class="qu-empty-state">
                <span class="dashicons dashicons-plugins-checked" aria-hidden="true"></span>
                <p>No plugins found on this page</p>
            </div>';
        }

        $html = '';
        $github_api = $this->parent->get_github_api();

        foreach ($plugins as $slug => $repo) {
            $version_info = $this->get_plugin_version_info($slug, $repo);
            if (!$version_info) {
                continue;
            }

            $has_error = isset($version_info['error']);
            $status_class = $has_error ? 'qu-error' : ($version_info['has_update'] ? 'qu-update-available' : 'qu-up-to-date');
            $status_text = $has_error ? __('Error checking updates', 'q-updater') :
                ($version_info['has_update'] ? __('Update Available', 'q-updater') : __('Up to Date', 'q-updater'));
            $is_active = is_plugin_active("$slug/$slug.php");
            $plugin_status_class = $is_active ? 'qu-status-active' : 'qu-status-inactive';
            $plugin_status_text = $is_active ? __('Active', 'q-updater') : __('Inactive', 'q-updater');

            // Determine if update button should be highlighted
            $update_button_class = $version_info['has_update'] ? 'qu-button-primary qu-pulse' : 'qu-button-secondary';
            $update_button_text = $version_info['has_update'] ? __('Update Now', 'q-updater') : __('Re-install', 'q-updater');
            $update_button_disabled = $has_error ? 'disabled' : '';

            $html .= '<div class="qu-plugin-card ' . ($version_info['has_update'] ? 'qu-card-highlight' : '') . '">';

            // Status indicator
            $html .= '<div class="qu-plugin-status-indicator ' . ($has_error ? 'qu-status-error' : ($version_info['has_update'] ? 'qu-status-update-available' : 'qu-status-up-to-date')) . '">';
            if ($version_info['has_update']) {
                $html .= '<span class="dashicons dashicons-warning" aria-hidden="true"></span>';
            } elseif (!$has_error) {
                $html .= '<span class="dashicons dashicons-yes-alt" aria-hidden="true"></span>';
            } else {
                $html .= '<span class="dashicons dashicons-warning" aria-hidden="true"></span>';
            }
            $html .= '</div>';

            // Plugin header
            $html .= '<div class="qu-plugin-header">';
            $html .= '<h3 class="qu-plugin-title">' . esc_html($version_info['name']) . '</h3>';
            $html .= '<div class="qu-plugin-status-badges">';
            $html .= '<span class="qu-version-status ' . esc_attr($status_class) . '">';
            if ($version_info['has_update']) {
                $html .= '<span class="dashicons dashicons-warning" aria-hidden="true"></span>';
            } elseif (!$has_error) {
                $html .= '<span class="dashicons dashicons-yes-alt" aria-hidden="true"></span>';
            } else {
                $html .= '<span class="dashicons dashicons-warning" aria-hidden="true"></span>';
            }
            $html .= esc_html($status_text) . '</span>';
            $html .= '<span class="qu-plugin-status ' . esc_attr($plugin_status_class) . '">';
            if ($is_active) {
                $html .= '<span class="dashicons dashicons-yes" aria-hidden="true"></span>';
            } else {
                $html .= '<span class="dashicons dashicons-marker" aria-hidden="true"></span>';
            }
            $html .= esc_html($plugin_status_text) . '</span>';
            $html .= '</div></div>';

            // Plugin meta
            $html .= '<div class="qu-plugin-meta">';
            $html .= '<div class="qu-plugin-versions">';
            $html .= '<div class="qu-version-item">';
            $html .= '<span class="qu-version-label">Current:</span>';
            $html .= '<span class="qu-version-number">' . esc_html($version_info['current_version']) . '</span>';
            $html .= '</div>';
            if ($version_info['latest_version']) {
                $html .= '<div class="qu-version-item ' . ($version_info['has_update'] ? 'qu-version-newer' : '') . '">';
                $html .= '<span class="qu-version-label">Latest:</span>';
                $html .= '<span class="qu-version-number">';
                if ($version_info['has_update']) {
                    $html .= '<span class="dashicons dashicons-arrow-up-alt" aria-hidden="true"></span>';
                }
                $html .= esc_html($version_info['latest_version']) . '</span>';
                $html .= '</div>';
            }
            $html .= '</div></div>';

            // Plugin actions
            $html .= '<div class="qu-plugin-actions">';
            $html .= '<button type="button" class="qu-plugin-action-btn ' . ($version_info['has_update'] ? 'qu-btn-primary' : 'qu-btn-success') . ' qu-update-plugin" ';
            $html .= 'data-plugin="' . esc_attr($slug) . '" ';
            $html .= 'data-version="' . esc_attr($version_info['latest_version'] ?: 'latest') . '" ' . $update_button_disabled . ' ';
            $html .= 'aria-label="' . esc_attr($update_button_text . ' ' . $version_info['name']) . '">';
            $html .= '<span class="dashicons dashicons-update" aria-hidden="true"></span>';
            $html .= esc_html($update_button_text) . '</button>';

            $html .= '<div class="qu-dropdown">';
            $html .= '<button type="button" class="qu-plugin-action-btn qu-dropdown-toggle" aria-haspopup="true" aria-expanded="false">';
            $html .= '<span class="dashicons dashicons-admin-tools" aria-hidden="true"></span> Actions';
            $html .= '<span class="dashicons dashicons-arrow-down-alt2" aria-hidden="true"></span>';
            $html .= '</button>';
            $html .= '<div class="qu-dropdown-menu">';
            $html .= '<button type="button" class="qu-dropdown-item qu-rollback-button" ';
            $html .= 'data-plugin="' . esc_attr($slug) . '" ';
            $html .= 'data-version="' . esc_attr($version_info['current_version']) . '" ';
            $html .= 'data-url="' . esc_url($github_api->get_github_download_url($repo, $version_info['current_version'])) . '" ';
            $html .= 'aria-label="Rollback ' . esc_attr($version_info['name']) . '">';
            $html .= '<span class="dashicons dashicons-backup" aria-hidden="true"></span> Rollback';
            $html .= '</button>';
            $html .= '<button type="button" class="qu-dropdown-item qu-uninstall-plugin qu-btn-danger" ';
            $html .= 'data-plugin="' . esc_attr($slug) . '" ';
            $html .= 'aria-label="Uninstall ' . esc_attr($version_info['name']) . '">';
            $html .= '<span class="dashicons dashicons-trash" aria-hidden="true"></span> Uninstall';
            $html .= '</button>';
            $html .= '</div></div></div>';

            // Error message
            if ($has_error) {
                $html .= '<div class="qu-plugin-error-message">';
                $html .= '<span class="dashicons dashicons-warning" aria-hidden="true"></span>';
                $html .= esc_html($version_info['error']);
                $html .= '</div>';
            }

            $html .= '</div>';
        }

        return $html;
    }

    /**
     * AJAX handler for saving plugins per page preference
     */
    public function ajax_save_plugins_per_page()
    {
        // Verify nonce
        if (!wp_verify_nonce($_POST['nonce'], 'q_updater_bulk_update')) {
            wp_send_json_error(__('Security check failed', 'q-updater'));
        }

        // Check user permissions
        if (!current_user_can('manage_options')) {
            wp_send_json_error(__('Insufficient permissions', 'q-updater'));
        }

        // Get and validate per_page parameter
        $per_page = isset($_POST['per_page']) ? intval($_POST['per_page']) : 6;
        $per_page = max(0, min(50, $per_page)); // Limit between 0 and 50

        // Save the preference
        update_option($this->parent->get_option_name('plugins_per_page'), $per_page);

        wp_send_json_success([
            'message' => __('Preference saved', 'q-updater'),
            'per_page' => $per_page
        ]);
    }
}
