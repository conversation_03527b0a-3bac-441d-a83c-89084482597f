<?php
/**
 * Cron Test Class
 *
 * Provides a simple admin page to test cron functionality.
 *
 * @package Q-Updater
 */

if (!defined('ABSPATH')) {
    exit; // Exit if accessed directly
}

class Q_Updater_Cron_Test
{
    private $parent;
    private $cron_manager;

    /**
     * Constructor
     *
     * @param Q_Updater $parent Parent class instance
     */
    public function __construct($parent)
    {
        $this->parent = $parent;
        $this->cron_manager = $parent->get_cron_manager();

        // Add admin page
        add_action('admin_menu', [$this, 'add_cron_test_page']);
        
        // Add AJAX handlers
        add_action('wp_ajax_qu_test_cron', [$this, 'ajax_test_cron']);
    }

    /**
     * Add cron test page to admin menu
     */
    public function add_cron_test_page()
    {
        add_submenu_page(
            null, // No parent menu
            'Q-Updater Cron Test',
            'Cron Test',
            'manage_options',
            'q-updater-cron-test',
            [$this, 'render_cron_test_page']
        );
    }

    /**
     * Render cron test page
     */
    public function render_cron_test_page()
    {
        // Check user capabilities
        if (!current_user_can('manage_options')) {
            wp_die(__('You do not have sufficient permissions to access this page.', 'q-updater'));
        }

        // Get the cron array
        $cron = _get_cron_array();

        // Get Q-Updater specific cron jobs
        $qu_hooks = [
            'q_updater_background_update',
            'q_updater_auto_update',
            'q_updater_check_updates',
            'qu_weekly_analytics_report'
        ];

        $qu_cron_jobs = [];
        foreach ($qu_hooks as $hook) {
            $timestamp = wp_next_scheduled($hook);
            $qu_cron_jobs[$hook] = $timestamp ? date('Y-m-d H:i:s', $timestamp) : 'Not scheduled';
        }

        // Get update frequency
        $update_frequency = get_option($this->parent->get_option_name('update_frequency'), 'daily');
        ?>
        <div class="qu-wrap">
            <h1>Q-Updater Cron Test</h1>
            
            <div class="notice notice-info">
                <p>This page allows you to test the cron functionality of the Q-Updater plugin.</p>
            </div>

            <h2>Current Q-Updater Cron Jobs</h2>
            <table class="widefat striped">
                <thead>
                    <tr>
                        <th>Hook</th>
                        <th>Next Run</th>
                    </tr>
                </thead>
                <tbody>
                    <?php foreach ($qu_cron_jobs as $hook => $next_run): ?>
                        <tr>
                            <td><?php echo esc_html($hook); ?></td>
                            <td><?php echo esc_html($next_run); ?></td>
                        </tr>
                    <?php endforeach; ?>
                </tbody>
            </table>

            <h2>Cron Test Actions</h2>
            <div class="qu-cron-actions">
                <div class="qu-cron-action-card">
                    <h3>Clean Up Cron Events</h3>
                    <p>This will clean up any orphaned or invalid cron events.</p>
                    <button type="button" class="button button-primary" id="qu-cleanup-cron">Clean Up Cron Events</button>
                </div>

                <div class="qu-cron-action-card">
                    <h3>Reschedule Background Update</h3>
                    <p>Current frequency: <?php echo esc_html($update_frequency); ?></p>
                    <button type="button" class="button button-primary" id="qu-reschedule-background-update">Reschedule Background Update</button>
                </div>

                <div class="qu-cron-action-card">
                    <h3>Reschedule Auto Update</h3>
                    <p>Current frequency: <?php echo esc_html($update_frequency); ?></p>
                    <button type="button" class="button button-primary" id="qu-reschedule-auto-update">Reschedule Auto Update</button>
                </div>

                <div class="qu-cron-action-card">
                    <h3>Reschedule Weekly Analytics Report</h3>
                    <p>This will schedule the weekly analytics report to run on the next Monday.</p>
                    <button type="button" class="button button-primary" id="qu-reschedule-analytics-report">Reschedule Weekly Analytics Report</button>
                </div>
            </div>

            <div id="qu-cron-test-results" class="qu-cron-test-results"></div>

            <h2>All WordPress Cron Events</h2>
            <div class="qu-cron-events">
                <?php if (empty($cron)): ?>
                    <p>No cron events scheduled.</p>
                <?php else: ?>
                    <table class="widefat striped">
                        <thead>
                            <tr>
                                <th>Timestamp</th>
                                <th>Hook</th>
                                <th>Schedule</th>
                                <th>Arguments</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($cron as $timestamp => $hooks): ?>
                                <?php foreach ($hooks as $hook => $events): ?>
                                    <?php foreach ($events as $key => $event): ?>
                                        <tr>
                                            <td><?php echo esc_html(date('Y-m-d H:i:s', $timestamp)); ?></td>
                                            <td><?php echo esc_html($hook); ?></td>
                                            <td><?php echo esc_html(isset($event['schedule']) ? $event['schedule'] : 'once'); ?></td>
                                            <td><?php echo esc_html(!empty($event['args']) ? json_encode($event['args']) : '[]'); ?></td>
                                        </tr>
                                    <?php endforeach; ?>
                                <?php endforeach; ?>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                <?php endif; ?>
            </div>
        </div>

        <style>
            .qu-cron-actions {
                display: flex;
                flex-wrap: wrap;
                gap: 20px;
                margin: 20px 0;
            }
            .qu-cron-action-card {
                background: #fff;
                border: 1px solid #ccd0d4;
                border-radius: 4px;
                padding: 15px;
                width: calc(50% - 10px);
                box-sizing: border-box;
            }
            .qu-cron-action-card h3 {
                margin-top: 0;
            }
            .qu-cron-test-results {
                background: #f8f9fa;
                border-left: 4px solid #2271b1;
                padding: 10px 15px;
                margin: 20px 0;
                display: none;
            }
            .qu-cron-test-results.success {
                border-left-color: #46b450;
            }
            .qu-cron-test-results.error {
                border-left-color: #dc3232;
            }
            .qu-cron-events {
                margin-top: 20px;
            }
        </style>

        <script>
            jQuery(document).ready(function($) {
                // Helper function to show results
                function showResults(message, isSuccess) {
                    const $results = $('#qu-cron-test-results');
                    $results.html(message);
                    $results.removeClass('success error').addClass(isSuccess ? 'success' : 'error');
                    $results.show();
                }

                // Helper function to make AJAX requests
                function testCronAction(action) {
                    $.ajax({
                        url: ajaxurl,
                        type: 'POST',
                        data: {
                            action: 'qu_test_cron',
                            nonce: '<?php echo wp_create_nonce('qu_test_cron'); ?>',
                            test_action: action
                        },
                        beforeSend: function() {
                            showResults('Processing...', true);
                        },
                        success: function(response) {
                            if (response.success) {
                                showResults(response.data.message, true);
                                
                                // Reload the page after a short delay to show updated cron jobs
                                setTimeout(function() {
                                    location.reload();
                                }, 2000);
                            } else {
                                showResults(response.data.message, false);
                            }
                        },
                        error: function() {
                            showResults('An error occurred while processing your request.', false);
                        }
                    });
                }

                // Clean up cron events
                $('#qu-cleanup-cron').on('click', function() {
                    testCronAction('cleanup');
                });

                // Reschedule background update
                $('#qu-reschedule-background-update').on('click', function() {
                    testCronAction('reschedule_background_update');
                });

                // Reschedule auto update
                $('#qu-reschedule-auto-update').on('click', function() {
                    testCronAction('reschedule_auto_update');
                });

                // Reschedule weekly analytics report
                $('#qu-reschedule-analytics-report').on('click', function() {
                    testCronAction('reschedule_analytics_report');
                });
            });
        </script>
        <?php
    }

    /**
     * AJAX handler for cron tests
     */
    public function ajax_test_cron()
    {
        // Check nonce
        if (!isset($_POST['nonce']) || !wp_verify_nonce($_POST['nonce'], 'qu_test_cron')) {
            wp_send_json_error(['message' => 'Security check failed.']);
        }

        // Check user capabilities
        if (!current_user_can('manage_options')) {
            wp_send_json_error(['message' => 'You do not have sufficient permissions to perform this action.']);
        }

        // Get the test action
        $action = isset($_POST['test_action']) ? sanitize_text_field($_POST['test_action']) : '';

        // Perform the requested action
        switch ($action) {
            case 'cleanup':
                $result = $this->cron_manager->cleanup_cron_option();
                wp_send_json_success([
                    'message' => $result ? 'Cron events cleaned up successfully.' : 'Failed to clean up cron events.',
                    'success' => $result
                ]);
                break;

            case 'reschedule_background_update':
                $frequency = get_option($this->parent->get_option_name('update_frequency'), 'daily');
                $result = $this->cron_manager->reschedule_event('q_updater_background_update', $frequency);
                wp_send_json_success([
                    'message' => is_wp_error($result) ? 'Failed to reschedule background update: ' . $result->get_error_message() : 'Background update rescheduled successfully.',
                    'success' => !is_wp_error($result)
                ]);
                break;

            case 'reschedule_auto_update':
                $frequency = get_option($this->parent->get_option_name('update_frequency'), 'daily');
                $result = $this->cron_manager->reschedule_event('q_updater_auto_update', $frequency);
                wp_send_json_success([
                    'message' => is_wp_error($result) ? 'Failed to reschedule auto update: ' . $result->get_error_message() : 'Auto update rescheduled successfully.',
                    'success' => !is_wp_error($result)
                ]);
                break;

            case 'reschedule_analytics_report':
                $next_monday = strtotime('next monday');
                $result = $this->cron_manager->schedule_event('qu_weekly_analytics_report', $next_monday, 'weekly');
                wp_send_json_success([
                    'message' => is_wp_error($result) ? 'Failed to reschedule weekly analytics report: ' . $result->get_error_message() : 'Weekly analytics report rescheduled successfully.',
                    'success' => !is_wp_error($result)
                ]);
                break;

            default:
                wp_send_json_error(['message' => 'Invalid action.']);
                break;
        }
    }
}
