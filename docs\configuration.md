# Q-Updater Configuration Guide

This guide provides detailed instructions for configuring the Q-Updater plugin to best suit your needs.

## Accessing the Settings Page

1. Log in to your WordPress admin dashboard
2. Navigate to **Settings > Q-Updater**
3. You'll see the main settings page with multiple tabs:
   - **Dashboard**: Overview of plugin status
   - **Plugins**: Manage Q plugins
   - **Settings**: Configure plugin options
   - **Tools**: Access additional tools like rollback
   - **Analytics**: View plugin usage statistics
   - **Reviews**: Submit and view plugin reviews

## General Settings

### GitHub API Token

A GitHub Personal Access Token allows Q-Update<PERSON> to access private repositories and increases the API rate limit.

**To configure your GitHub token:**

1. Go to [GitHub Personal Access Tokens](https://github.com/settings/tokens/new)
2. Create a new token with the following scopes:
   - `repo` (for full repository access)
   - `read:packages` (if you use GitHub Packages)
3. Copy the generated token
4. In Q-Updater settings, paste the token in the "GitHub Access Token" field
5. Click "Save Changes"

**Security Note:** Your token is encrypted before being stored in the WordPress database for enhanced security.

### Update Frequency

Control how often Q-Updater checks for plugin updates:

| Setting     | Description                        | Recommended For                   |
| ----------- | ---------------------------------- | --------------------------------- |
| Hourly      | Check every hour                   | Development environments          |
| Daily       | Check once per day                 | Most websites                     |
| Weekly      | Check once per week                | Stable production sites           |
| Monthly     | Check once per month               | Very stable sites                 |
| Manual Only | Only check when manually triggered | Sites with strict update policies |

To configure:

1. In the Settings tab, select your preferred frequency from the "Update Frequency" dropdown
2. Click "Save Changes"

### Notification Settings

Configure how you want to be notified about updates:

#### Email Notifications

1. Check the "Email Notifications" box
2. Enter the email address where you want to receive notifications
3. Select which events trigger notifications:
   - Updates Available
   - Updates Installed
   - Update Failures
   - Security Alerts

#### Dashboard Notifications

1. Check the "Dashboard Notifications" box to enable admin notices
2. Configure notification persistence (how long they remain visible)

## Custom Repository Mappings

By default, Q-Updater assumes plugins with names starting with "q-" are hosted in GitHub repositories with the format "shamielo/q-pluginname". If your plugins use different repository names, you can configure custom mappings:

1. In the Settings tab, scroll to the "Custom Repository Mappings" section
2. For each plugin, enter the GitHub repository in the format "username/repository"
3. Click "Save Changes"

**Example:**

- Plugin: q-seo
- Default Repository: shamielo/q-seo
- Custom Mapping: your-username/custom-seo-repo

## Auto-Update Settings

Configure which plugins should be automatically updated:

1. Navigate to the **Plugins** tab
2. For each plugin, check the "Auto-Update" box if you want it to update automatically
3. Click "Save Changes"

You can also configure auto-update settings for all plugins at once:

1. Click the "Select All" checkbox at the top of the list
2. Choose "Enable Auto-Updates" from the bulk actions dropdown
3. Click "Apply"

## Advanced Settings

### Backup Settings

Configure plugin backups before updates:

1. In the Settings tab, scroll to the "Backup Settings" section
2. Check "Create Backups" to enable automatic backups before updates
3. Set the number of backups to keep per plugin
4. Click "Save Changes"

### Developer Email

If you're a plugin developer, you can configure Q-Updater to send analytics and review data to your email:

1. Enter your email address in the "Developer Email" field
2. Select which data you want to receive:
   - Analytics Reports
   - User Reviews
   - Error Reports
3. Click "Save Changes"

### Security Settings

Configure additional security options:

1. In the Settings tab, scroll to the "Security Settings" section
2. Configure token expiration reminders
3. Set API request limits
4. Enable/disable secure token storage
5. Click "Save Changes"

## Saving Your Configuration

After making any changes to the settings, always click the "Save Changes" button at the bottom of the page to apply your configuration.

## Exporting and Importing Settings

If you need to transfer your Q-Updater configuration to another site:

1. Navigate to the **Tools** tab
2. Click "Export Settings" to download a JSON file with your configuration
3. On the target site, navigate to the Tools tab
4. Click "Import Settings" and select your JSON file
5. Click "Import" to apply the configuration

## Resetting to Default Settings

If you need to reset Q-Updater to its default configuration:

1. Navigate to the **Tools** tab
2. Click "Reset Settings"
3. Confirm the reset

**Warning:** This will remove all custom settings and return the plugin to its default state.

---

For more detailed information on specific features, please refer to the [User Guide](user-guide.md).
