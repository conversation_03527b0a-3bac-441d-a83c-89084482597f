<?php
/**
 * GitHub Token Debug Tool
 * 
 * This script tests your GitHub token and provides detailed information about any issues.
 * Place this file in your WordPress plugin directory and access it via the browser.
 * 
 * IMPORTANT: Delete this file after use for security reasons.
 */

// Load WordPress
require_once dirname(dirname(dirname(dirname(__FILE__)))) . '/wp-load.php';

// Security check - only allow admin users
if (!current_user_can('manage_options')) {
    wp_die('Access denied');
}

// Get Q-Updater instance
global $q_updater;
if (!$q_updater) {
    echo '<p>Error: Q-Updater plugin not found or not active.</p>';
    exit;
}

// Get token manager
$token_manager = $q_updater->get_token_manager();
if (!$token_manager) {
    echo '<p>Error: Token manager not available.</p>';
    exit;
}

// Get security component
$security = $q_updater->get_security();
if (!$security) {
    echo '<p>Error: Security component not available.</p>';
    exit;
}

// Get GitHub API component
$github_api = $q_updater->get_github_api();
if (!$github_api) {
    echo '<p>Error: GitHub API component not available.</p>';
    exit;
}

// Test token
$token_test = $token_manager->test_token();

// Output HTML header
?>
<!DOCTYPE html>
<html>
<head>
    <title>GitHub Token Debug</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            line-height: 1.6;
            margin: 20px;
            max-width: 1200px;
        }
        .card {
            border: 1px solid #ddd;
            border-radius: 4px;
            padding: 20px;
            margin-bottom: 20px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .success {
            color: green;
            font-weight: bold;
        }
        .warning {
            color: orange;
            font-weight: bold;
        }
        .error {
            color: red;
            font-weight: bold;
        }
        table {
            width: 100%;
            border-collapse: collapse;
        }
        table, th, td {
            border: 1px solid #ddd;
        }
        th, td {
            padding: 10px;
            text-align: left;
        }
        th {
            background-color: #f2f2f2;
        }
        pre {
            background-color: #f5f5f5;
            padding: 10px;
            border-radius: 4px;
            overflow-x: auto;
        }
    </style>
</head>
<body>
    <h1>GitHub Token Debug</h1>
    
    <div class="card">
        <h2>Token Status</h2>
        <?php if ($token_test['status'] === 'success'): ?>
            <p class="success">✅ Token is valid</p>
        <?php elseif ($token_test['status'] === 'warning'): ?>
            <p class="warning">⚠️ Token has issues</p>
        <?php else: ?>
            <p class="error">❌ Token is invalid</p>
        <?php endif; ?>
        
        <p><strong>Message:</strong> <?php echo esc_html($token_test['message']); ?></p>
        
        <?php if (isset($token_test['user'])): ?>
            <p><strong>Authenticated as:</strong> <?php echo esc_html($token_test['user']['login']); ?></p>
        <?php endif; ?>
        
        <?php if (isset($token_test['scopes'])): ?>
            <p><strong>Token Scopes:</strong> <?php echo esc_html($token_test['scopes']); ?></p>
        <?php endif; ?>
        
        <?php if (isset($token_test['rate_limit'])): ?>
            <h3>Rate Limit Information</h3>
            <p><strong>Limit:</strong> <?php echo esc_html($token_test['rate_limit']['limit']); ?></p>
            <p><strong>Remaining:</strong> <?php echo esc_html($token_test['rate_limit']['remaining']); ?></p>
            <p><strong>Reset Time:</strong> <?php echo esc_html($token_test['rate_limit']['reset']); ?></p>
        <?php endif; ?>
    </div>
    
    <div class="card">
        <h2>Test Repository Access</h2>
        <form method="post">
            <p>
                <label for="repo">Repository (username/repository):</label>
                <input type="text" id="repo" name="repo" value="<?php echo isset($_POST['repo']) ? esc_attr($_POST['repo']) : ''; ?>" style="width: 300px;">
                <button type="submit">Test Access</button>
            </p>
        </form>
        
        <?php
        if (isset($_POST['repo']) && !empty($_POST['repo'])) {
            $repo = sanitize_text_field($_POST['repo']);
            
            // Validate repository name
            $is_valid_format = $security->validate_repository_name($repo);
            
            echo '<h3>Repository: ' . esc_html($repo) . '</h3>';
            
            if (!$is_valid_format) {
                echo '<p class="error">❌ Invalid repository format. Use: username/repository</p>';
            } else {
                echo '<p class="success">✅ Valid repository format</p>';
                
                // Test repository access by fetching releases
                $releases = $github_api->get_all_releases($repo);
                
                if (is_wp_error($releases)) {
                    echo '<p class="error">❌ Error accessing repository: ' . esc_html($releases->get_error_message()) . '</p>';
                } else {
                    echo '<p class="success">✅ Successfully accessed repository</p>';
                    echo '<p>Found ' . count($releases) . ' releases</p>';
                    
                    if (!empty($releases)) {
                        echo '<table>';
                        echo '<tr><th>Version</th><th>Published</th><th>Download URL</th></tr>';
                        
                        foreach ($releases as $release) {
                            echo '<tr>';
                            echo '<td>' . esc_html($release['version']) . '</td>';
                            echo '<td>' . esc_html($release['published_at']) . '</td>';
                            echo '<td>' . esc_html($release['download_url']) . '</td>';
                            echo '</tr>';
                        }
                        
                        echo '</table>';
                    }
                }
            }
        }
        ?>
    </div>
    
    <p><strong>Warning:</strong> Delete this file after use for security reasons.</p>
</body>
</html>
